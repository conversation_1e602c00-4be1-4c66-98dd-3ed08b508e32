import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.guardrail_agent import GuardrailAgent
import json
import yaml
import time

def load_demo_scenarios():
    with open('demo/demo_scenarios.yaml') as f:
        return yaml.safe_load(f)

def run_demo():
    print("🚀 AI-Powered Guardrail Agent Demonstration\n")

    # Initialize agent - you need to set your OpenAI API key
    import os
    api_key = os.getenv('OPENAI_API_KEY', 'your-openai-api-key')
    if api_key == 'your-openai-api-key':
        print("⚠️  Please set your OPENAI_API_KEY environment variable")
        return

    guardrail = GuardrailAgent(api_key)
    
    # Load scenarios
    scenarios = load_demo_scenarios()
    
    for scenario in scenarios['scenarios']:
        print(f"\n=== SCENARIO: {scenario['name']} ===")
        print(f"Description: {scenario['description']}")
        
        # Load test data
        with open(f"test_data/{scenario['test_data']}") as f:
            agent_output = json.load(f)

        # Apply modifications if specified
        if 'modifications' in scenario:
            for mod in scenario['modifications']:
                path_parts = mod['path'].split('.')
                current = agent_output
                for part in path_parts[:-1]:
                    current = current[part]
                current[path_parts[-1]] = mod['value']

        # Run audit
        start_time = time.time()
        audit_report = guardrail.audit_agent_output(agent_output)
        processing_time = (time.time() - start_time) * 1000
        
        # Display results
        print(f"\n🛡️ Guardrail Audit Completed in {processing_time:.0f}ms")
        print(f"Audit Status: {audit_report['status']}")
        
        if audit_report['status'] != 'PASSED':
            print("\n🔍 Findings:")
            for category, findings in audit_report['findings'].items():
                if findings:
                    print(f"  {category.upper()}:")
                    for finding in findings:
                        print(f"    - {finding}")
        
        if 'escalation' in audit_report:
            print(f"\n🚨 ESCALATION REQUIRED:")
            print(audit_report['escalation'])
        
        print("\n" + "-"*50)

if __name__ == "__main__":
    run_demo()