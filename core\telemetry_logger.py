import json
import logging
from datetime import datetime

class TelemetryLogger:
    def __init__(self, log_file="audit_logs.jsonl"):
        self.log_file = log_file
        self._setup_console_logger()

    def _setup_console_logger(self):
        """Setup a human-readable console logger"""
        self.console_logger = logging.getLogger("GuardrailLogger")
        self.console_logger.setLevel(logging.INFO)

        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - GuardrailAgent - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)

        if not self.console_logger.handlers:
            self.console_logger.addHandler(handler)

    def log_audit_start(self, metadata):
        self.console_logger.info("[START] Auditing output from {}".format(metadata.get("agent_type", "UNKNOWN")))
        self._write_log({
            "timestamp": datetime.utcnow().isoformat(),
            "event": "AUDIT_START",
            "agent_type": metadata.get("agent_type", "UNKNOWN"),
            "agent_version": metadata.get("agent_version", "UNKNOWN"),
            "task_id": metadata.get("task_id", "UNKNOWN")
        })

    def log_audit_complete(self, report):
        findings = report.get("findings", {})
        status = report.get("status", "UNKNOWN")

        self.console_logger.info(f"[DONE] Audit completed with status: {status}")
        if findings.get("process_issues"):
            self.console_logger.warning(f"[WARN] Process issues: {len(findings['process_issues'])}")
        if findings.get("reasoning_issues"):
            self.console_logger.warning(f"[WARN] Reasoning issues: {len(findings['reasoning_issues'])}")
        if findings.get("security_issues"):
            self.console_logger.warning(f"[WARN] Security issues: {len(findings['security_issues'])}")

        self._write_log({
            "timestamp": datetime.utcnow().isoformat(),
            "event": "AUDIT_COMPLETE",
            "agent_type": report.get("agent_type", "UNKNOWN"),
            "status": status,
            "processing_time_ms": report.get("processing_time_ms", 0),
            "findings_count": {
                "process_issues": len(findings.get("process_issues", [])),
                "reasoning_issues": len(findings.get("reasoning_issues", [])),
                "security_issues": len(findings.get("security_issues", []))
            }
        })

    def log_escalation(self, escalation):
        self.console_logger.info("[ESCALATE] Human review triggered due to: {}".format(escalation.get("reason", "unknown reason")))
        self._write_log({
            "timestamp": datetime.utcnow().isoformat(),
            "event": "ESCALATION",
            "reason": escalation.get("reason", "Unknown"),
            "severity": escalation.get("severity", "MEDIUM"),
            "task_id": escalation.get("task_id", "UNKNOWN")
        })

    def _write_log(self, log_entry):
        """Append log entry to file"""
        try:
            with open(self.log_file, "a", encoding='utf-8') as f:
                f.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            self.console_logger.warning(f"[ERROR] Failed to write log to {self.log_file}: {e}")
